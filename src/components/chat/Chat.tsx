"use client"

import { MessageList } from "./MessageList"
import { ChatInput } from "./ChatInput"
import { ChatMessage, ChatState } from "@/types/chat"
import { cn } from "@/ui/lib/utils"

interface ChatProps {
  messages: ChatMessage[]
  isLoading?: boolean
  error?: string | null
  onSendMessage: (message: string) => void
  onStopGeneration?: () => void
  className?: string
}

export function Chat({
  messages,
  isLoading = false,
  error,
  onSendMessage,
  onStopGeneration,
  className,
}: ChatProps) {
  return (
    <div className={cn("flex h-full flex-col", className)}>
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4">
          <h1 className="text-lg font-semibold">Chat Assistant</h1>
        </div>
      </div>

      {/* Error Banner */}
      {error && (
        <div className="border-b bg-destructive/10 px-4 py-2">
          <div className="text-sm text-destructive">
            Error: {error}
          </div>
        </div>
      )}

      {/* Messages */}
      <MessageList
        messages={messages}
        isLoading={isLoading}
        className="flex-1"
      />

      {/* Input */}
      <ChatInput
        onSendMessage={onSendMessage}
        onStopGeneration={onStopGeneration}
        isLoading={isLoading}
        disabled={!!error}
      />
    </div>
  )
}