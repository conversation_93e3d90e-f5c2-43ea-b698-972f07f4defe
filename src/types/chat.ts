export interface ChatMessage {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
  isStreaming?: boolean
}

export interface ChatRequest {
  messages: Omit<ChatMessage, "id" | "timestamp" | "isStreaming">[]
  model?: string
  temperature?: number
  max_tokens?: number
}

export interface ChatResponse {
  content: string
  error?: string
}

export interface ChatState {
  messages: ChatMessage[]
  isLoading: boolean
  error: string | null
}

export type MessageRole = ChatMessage["role"]

// 用于流式响应的事件类型
export interface StreamChunk {
  content: string
}

// 聊天配置
export interface ChatConfig {
  model: string
  temperature: number
  max_tokens: number
}

// 默认配置
export const DEFAULT_CHAT_CONFIG: ChatConfig = {
  model: "gpt-3.5-turbo",
  temperature: 0.7,
  max_tokens: 2048,
}