"use client"

import { useState, useCallback, useRef } from "react"
import { ChatMessage, ChatState, DEFAULT_CHAT_CONFIG } from "@/types/chat"

interface UseChatOptions {
  initialMessages?: ChatMessage[]
  model?: string
  temperature?: number
  max_tokens?: number
}

export function useChat(options: UseChatOptions = {}) {
  const {
    initialMessages = [],
    model = DEFAULT_CHAT_CONFIG.model,
    temperature = DEFAULT_CHAT_CONFIG.temperature,
    max_tokens = DEFAULT_CHAT_CONFIG.max_tokens,
  } = options

  const [state, setState] = useState<ChatState>({
    messages: initialMessages,
    isLoading: false,
    error: null,
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  // 生成唯一 ID
  const generateId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // 添加消息
  const addMessage = useCallback((message: Omit<ChatMessage, "id" | "timestamp">) => {
    const newMessage: ChatMessage = {
      ...message,
      id: generateId(),
      timestamp: new Date(),
    }

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
    }))

    return newMessage
  }, [])

  // 更新消息内容
  const updateMessage = useCallback((id: string, updates: Partial<ChatMessage>) => {
    setState(prev => ({
      ...prev,
      messages: prev.messages.map(msg =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }))
  }, [])

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (state.isLoading) return

    // 添加用户消息
    const userMessage = addMessage({
      role: "user",
      content,
    })

    // 设置加载状态
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
    }))

    // 创建助手消息占位符
    const assistantMessage = addMessage({
      role: "assistant",
      content: "",
      isStreaming: true,
    })

    try {
      // 创建 AbortController 用于取消请求
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // 准备请求数据
      const messages = [...state.messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content,
      }))

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages,
          model,
          temperature,
          max_tokens,
        }),
        signal: abortController.signal,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error("No response body")
      }

      let assistantContent = ""

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split("\n")

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6).trim()

            if (data === "[DONE]") {
              // 流结束
              updateMessage(assistantMessage.id, {
                isStreaming: false,
              })
              break
            }

            try {
              const parsed = JSON.parse(data)
              if (parsed.content) {
                assistantContent += parsed.content
                updateMessage(assistantMessage.id, {
                  content: assistantContent,
                })
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      console.error("Chat error:", error)

      // 移除助手消息占位符
      setState(prev => ({
        ...prev,
        messages: prev.messages.filter(msg => msg.id !== assistantMessage.id),
        error: error instanceof Error ? error.message : "An error occurred",
      }))
    } finally {
      setState(prev => ({
        ...prev,
        isLoading: false,
      }))
      abortControllerRef.current = null
    }
  }, [state.messages, state.isLoading, addMessage, updateMessage, model, temperature, max_tokens])

  // 停止生成
  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    setState(prev => ({
      ...prev,
      isLoading: false,
      messages: prev.messages.map(msg => ({
        ...msg,
        isStreaming: false,
      })),
    }))
  }, [])

  // 清除错误
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
    }))
  }, [])

  // 重置聊天
  const reset = useCallback(() => {
    stopGeneration()
    setState({
      messages: initialMessages,
      isLoading: false,
      error: null,
    })
  }, [initialMessages, stopGeneration])

  return {
    messages: state.messages,
    isLoading: state.isLoading,
    error: state.error,
    sendMessage,
    stopGeneration,
    clearError,
    reset,
  }
}